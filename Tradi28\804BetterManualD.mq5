//+------------------------------------------------------------------+
//|                V75_1s_SpikeHunter_RELAXED.mq5                   |
//|          🚀 RELAXED VERSION - CATCH MORE SPIKES & PROFIT!       |
//|                   Trading on 1-minute timeframe                 |
//|          📉 Loosened constraints for better trade frequency     |
//+------------------------------------------------------------------+
#property copyright "2025"
#property version   "5.2"
#property strict

// === V75 1s INDEX OPTIMIZED SWEET SPOT ZONES (1-MINUTE TIMEFRAME) ===
input group "=== RELAXED SWEET SPOT ZONES - CATCH MORE SPIKES ==="
input bool     UseSweetSpots = true;
// 📉 RELAXED for V75 1s - Catch smaller spikes and lower body ratios
input double   SweetSpot1_Min = 30.0;      // 📉 SMALLER spikes (was 50)
input double   SweetSpot1_Max = 200.0;
input double   SweetSpot1_Ratio = 0.25;    // 📉 MUCH LOWER body ratio (was 0.35)

input double   SweetSpot2_Min = 200.0;     // Medium spikes
input double   SweetSpot2_Max = 500.0;
input double   SweetSpot2_Ratio = 0.30;    // 📉 LOWER (was 0.40)

input double   SweetSpot3_Min = 500.0;     // Large spikes
input double   SweetSpot3_Max = 1000.0;
input double   SweetSpot3_Ratio = 0.35;    // 📉 LOWER (was 0.45)

input double   SweetSpot4_Min = 1000.0;    // Extra large spikes
input double   SweetSpot4_Max = 2000.0;
input double   SweetSpot4_Ratio = 0.40;    // 📉 LOWER (was 0.50)

input group "=== BASE PARAMETERS ==="
input double   BaseSpikeSize = 50.0;       // Increased for V75 reality
input double   BaseBodyRatio = 0.35;       // Lowered to catch more signals
input double   BaseSpikeFactor = 1.2;      // Slightly reduced ATR factor

input group "=== TRADE MANAGEMENT ==="
input double   RiskPercent = 0.5;
input double   FixedLot = 0.1;             // Conservative lot size
input bool     UseFixedLot = true;          // Default to fixed lot to avoid volume errors
input double   MaxLot = 1.0;               // Maximum lot size limit
input double   RRRatio = 2.0;              // Balanced for better hit rate
input double   TrailPips = 8.0;            // Increased trailing distance
input int      MaxSlippage = 100;          // Increased for V75 volatility
input int      CooldownSeconds = 10;       // 📉 REDUCED cooldown - More opportunities (was 20)
input double   ATRMultiplier = 1.2;        // Slightly tighter SL
input double   SpikeMultiplier = 0.22;     // Slightly tighter SL

input group "=== SIGNAL FILTERING ==="
input bool     UseEMAFilter = false;       // 🚫 DISABLED - Too restrictive, blocking profitable trades
input bool     UseContrarian = false;      // Trade WITH spikes (momentum/trend-following)
input double   MinATRMultiplier = 0.8;     // 📉 RELAXED - Catch smaller spikes too
input bool     UseRSIFilter = true;        // RSI momentum confirmation
input double   RSI_Oversold = 35.0;        // 📉 MORE RELAXED - Catch more oversold conditions
input double   RSI_Overbought = 65.0;      // 📈 MORE RELAXED - Catch more overbought conditions
input bool     UseMACDFilter = true;       // MACD trend confirmation
input bool     UseStochFilter = true;      // Stochastic momentum filter
input bool     RequireMultiConfirm = true;  // Require multiple confirmations but RELAXED
input int      MinConfirmations = 1;         // 📉 RELAXED - Only need 1 confirmation instead of 2

input group "=== ENHANCED LEARNING SYSTEM ==="
input bool     EnableEnhancedLearning = true;    // Enable enhanced reward-based learning
input bool     UseHourlyFiltering = true;        // Filter trades based on hourly performance
input bool     UseConfidenceScoring = true;      // Use confidence-based trade filtering
input bool     UseAdaptiveParameters = true;     // Enable adaptive TP/SL adjustment
input bool     UseBreakevenProtection = true;    // Enable breakeven mechanism
input double   MinInitialConfidence = 40.0;      // Minimum confidence for initial trades
input int      LearningPeriod = 10;              // Adjust parameters every N trades

input group "=== TRACKING ==="
input bool     TrackResults = true;
input bool     ShowDebug = true;
input bool     EnableTestMode = false;

// Global Variables
int atrHandle, emaFastHandle, emaSlowHandle;
int rsiHandle, macdHandle, stochHandle;
datetime lastTradeTime = 0;
int totalTrades = 0, winTrades = 0;
double lastATR = 0;
double minLot = 0, maxLot = 0, lotStep = 0;
double adjustedFixedLot = 0;

// ENHANCED REWARD-BASED LEARNING SYSTEM
struct IndicatorCombo {
   string pattern;        // "RSI+MACD", "MACD+STOCH", "RSI+STOCH", "ALL_THREE"
   int trades;           // Total trades with this combo
   double totalProfit;   // Total profit/loss
   double winRate;       // Win percentage
   double avgProfit;     // Average profit per trade
   double score;         // Performance score
   double confidence;    // Confidence level (0-100)
   int consecutiveWins;  // Current win streak
   int consecutiveLosses; // Current loss streak
   int maxWinStreak;     // Best win streak achieved
   int maxLossStreak;    // Worst loss streak
};

// TIME-OF-DAY PERFORMANCE TRACKING
struct HourlyPerformance {
   int hour;             // Hour of day (0-23)
   int trades;           // Total trades in this hour
   double totalProfit;   // Total profit/loss for this hour
   double winRate;       // Win rate for this hour
   double avgProfit;     // Average profit per trade
   double score;         // Performance score for this hour
   bool isActive;        // Whether this hour is profitable enough to trade
};

// ADAPTIVE PARAMETERS SYSTEM
struct AdaptiveParams {
   double currentTPMultiplier;    // Current TP multiplier (starts at RRRatio)
   double currentSLMultiplier;    // Current SL multiplier (starts at ATRMultiplier)
   double currentMinConfidence;   // Minimum confidence required for trades
   int currentCooldown;           // Current cooldown period
   double tpAdjustmentFactor;     // How much to adjust TP based on performance
   double slAdjustmentFactor;     // How much to adjust SL based on performance
   datetime lastAdjustment;       // When parameters were last adjusted
};

// STREAK TRACKING SYSTEM
struct StreakTracker {
   int currentWinStreak;          // Current consecutive wins
   int currentLossStreak;         // Current consecutive losses
   int maxWinStreak;              // Best win streak achieved
   int maxLossStreak;             // Worst loss streak
   datetime lastWin;              // Time of last winning trade
   datetime lastLoss;             // Time of last losing trade
   bool isOnWinStreak;            // Currently on a winning streak
   bool isOnLossStreak;           // Currently on a losing streak
};

IndicatorCombo combos[4];
HourlyPerformance hourlyStats[24];
AdaptiveParams adaptiveParams;
StreakTracker streakTracker;
bool learningInitialized = false;
string lastTradePattern = "";  // Track what pattern was used for last trade
double lastTradeProfit = 0.0;  // Track last trade result for streak calculation

//+------------------------------------------------------------------+
//| Initialize Enhanced Learning System                              |
//+------------------------------------------------------------------+
void InitializeLearning()
{
   if(learningInitialized) return;

   // Initialize indicator combinations
   combos[0].pattern = "RSI+MACD";
   combos[1].pattern = "MACD+STOCH";
   combos[2].pattern = "RSI+STOCH";
   combos[3].pattern = "ALL_THREE";

   for(int i = 0; i < 4; i++) {
      combos[i].trades = 0;
      combos[i].totalProfit = 0.0;
      combos[i].winRate = 0.0;
      combos[i].avgProfit = 0.0;
      combos[i].score = 0.0;
      combos[i].confidence = 50.0;  // Start with neutral confidence
      combos[i].consecutiveWins = 0;
      combos[i].consecutiveLosses = 0;
      combos[i].maxWinStreak = 0;
      combos[i].maxLossStreak = 0;
   }

   // Initialize hourly performance tracking
   for(int h = 0; h < 24; h++) {
      hourlyStats[h].hour = h;
      hourlyStats[h].trades = 0;
      hourlyStats[h].totalProfit = 0.0;
      hourlyStats[h].winRate = 0.0;
      hourlyStats[h].avgProfit = 0.0;
      hourlyStats[h].score = 0.0;
      hourlyStats[h].isActive = true;  // Start with all hours active
   }

   // Initialize adaptive parameters
   adaptiveParams.currentTPMultiplier = RRRatio;
   adaptiveParams.currentSLMultiplier = ATRMultiplier;
   adaptiveParams.currentMinConfidence = 40.0;  // Start with lower confidence requirement
   adaptiveParams.currentCooldown = CooldownSeconds;
   adaptiveParams.tpAdjustmentFactor = 0.1;     // 10% adjustment per iteration
   adaptiveParams.slAdjustmentFactor = 0.05;    // 5% adjustment per iteration
   adaptiveParams.lastAdjustment = 0;

   // Initialize streak tracker
   streakTracker.currentWinStreak = 0;
   streakTracker.currentLossStreak = 0;
   streakTracker.maxWinStreak = 0;
   streakTracker.maxLossStreak = 0;
   streakTracker.lastWin = 0;
   streakTracker.lastLoss = 0;
   streakTracker.isOnWinStreak = false;
   streakTracker.isOnLossStreak = false;

   learningInitialized = true;
   Print("🧠 ENHANCED REWARD-BASED LEARNING SYSTEM INITIALIZED");
   Print("📊 Time-of-day tracking: ENABLED");
   Print("🎯 Adaptive parameters: ENABLED");
   Print("🔥 Streak tracking: ENABLED");
}

//+------------------------------------------------------------------+
//| Update Enhanced Learning System with Trade Result               |
//+------------------------------------------------------------------+
void UpdateLearning(string pattern, double profit)
{
   if(!learningInitialized || pattern == "") return;

   // Store for streak tracking
   lastTradeProfit = profit;
   bool isWin = profit > 0.01;

   // Update streak tracking
   UpdateStreakTracking(isWin);

   // Update hourly performance
   UpdateHourlyPerformance(profit);

   // Find the combo index
   int comboIndex = -1;
   for(int i = 0; i < 4; i++) {
      if(combos[i].pattern == pattern ||
         (pattern == "RSI+MACD+STOCH" && combos[i].pattern == "ALL_THREE")) {
         comboIndex = i;
         break;
      }
   }

   if(comboIndex == -1) return; // Pattern not found

   // Update basic statistics
   combos[comboIndex].trades++;
   combos[comboIndex].totalProfit += profit;
   combos[comboIndex].avgProfit = combos[comboIndex].totalProfit / combos[comboIndex].trades;

   // Update win/loss streaks for this pattern
   if(isWin) {
      combos[comboIndex].consecutiveWins++;
      combos[comboIndex].consecutiveLosses = 0;
      if(combos[comboIndex].consecutiveWins > combos[comboIndex].maxWinStreak) {
         combos[comboIndex].maxWinStreak = combos[comboIndex].consecutiveWins;
      }
   } else {
      combos[comboIndex].consecutiveLosses++;
      combos[comboIndex].consecutiveWins = 0;
      if(combos[comboIndex].consecutiveLosses > combos[comboIndex].maxLossStreak) {
         combos[comboIndex].maxLossStreak = combos[comboIndex].consecutiveLosses;
      }
   }

   // Calculate win rate
   if(isWin) {
      combos[comboIndex].winRate = ((combos[comboIndex].winRate * (combos[comboIndex].trades - 1)) + 100.0) / combos[comboIndex].trades;
   } else {
      combos[comboIndex].winRate = (combos[comboIndex].winRate * (combos[comboIndex].trades - 1)) / combos[comboIndex].trades;
   }

   // Calculate performance score (win rate * avg profit)
   combos[comboIndex].score = combos[comboIndex].winRate * combos[comboIndex].avgProfit;

   // Update confidence based on recent performance
   UpdateConfidenceScore(comboIndex);

   // Trigger adaptive parameter adjustment
   AdaptParameters();

   Print("📊 ENHANCED LEARNING UPDATE: ", pattern, " | Profit: $", DoubleToString(profit, 2),
         " | Trades: ", combos[comboIndex].trades,
         " | Win Rate: ", DoubleToString(combos[comboIndex].winRate, 1), "%",
         " | Confidence: ", DoubleToString(combos[comboIndex].confidence, 1), "%",
         " | Win Streak: ", combos[comboIndex].consecutiveWins,
         " | Score: ", DoubleToString(combos[comboIndex].score, 2));
}

//+------------------------------------------------------------------+
//| Update Streak Tracking System                                   |
//+------------------------------------------------------------------+
void UpdateStreakTracking(bool isWin)
{
   if(isWin) {
      streakTracker.currentWinStreak++;
      streakTracker.currentLossStreak = 0;
      streakTracker.lastWin = TimeCurrent();
      streakTracker.isOnWinStreak = true;
      streakTracker.isOnLossStreak = false;

      if(streakTracker.currentWinStreak > streakTracker.maxWinStreak) {
         streakTracker.maxWinStreak = streakTracker.currentWinStreak;
         Print("🔥 NEW WIN STREAK RECORD: ", streakTracker.maxWinStreak, " consecutive wins!");
      }
   } else {
      streakTracker.currentLossStreak++;
      streakTracker.currentWinStreak = 0;
      streakTracker.lastLoss = TimeCurrent();
      streakTracker.isOnLossStreak = true;
      streakTracker.isOnWinStreak = false;

      if(streakTracker.currentLossStreak > streakTracker.maxLossStreak) {
         streakTracker.maxLossStreak = streakTracker.currentLossStreak;
         Print("⚠️ LOSS STREAK WARNING: ", streakTracker.maxLossStreak, " consecutive losses");
      }
   }
}

//+------------------------------------------------------------------+
//| Update Hourly Performance Statistics                            |
//+------------------------------------------------------------------+
void UpdateHourlyPerformance(double profit)
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   int currentHour = dt.hour;

   hourlyStats[currentHour].trades++;
   hourlyStats[currentHour].totalProfit += profit;
   hourlyStats[currentHour].avgProfit = hourlyStats[currentHour].totalProfit / hourlyStats[currentHour].trades;

   // Calculate win rate for this hour
   static int hourlyWins[24] = {0};
   if(profit > 0.01) hourlyWins[currentHour]++;

   hourlyStats[currentHour].winRate = hourlyStats[currentHour].trades > 0 ?
      (double)hourlyWins[currentHour] / hourlyStats[currentHour].trades * 100.0 : 0.0;

   // Calculate performance score
   hourlyStats[currentHour].score = hourlyStats[currentHour].winRate * hourlyStats[currentHour].avgProfit;

   // Determine if this hour should remain active (win rate > 40% and positive avg profit)
   if(hourlyStats[currentHour].trades >= 10) {
      hourlyStats[currentHour].isActive = (hourlyStats[currentHour].winRate > 40.0 &&
                                          hourlyStats[currentHour].avgProfit > 0.0);
   }
}

//+------------------------------------------------------------------+
//| Update Confidence Score for Pattern                             |
//+------------------------------------------------------------------+
void UpdateConfidenceScore(int comboIndex)
{
   if(comboIndex < 0 || comboIndex >= 4) return;

   double baseConfidence = combos[comboIndex].winRate;
   double streakBonus = 0.0;
   double tradeVolumeBonus = 0.0;

   // Bonus for win streaks, penalty for loss streaks
   if(combos[comboIndex].consecutiveWins > 0) {
      streakBonus = MathMin(combos[comboIndex].consecutiveWins * 5.0, 25.0); // Max 25% bonus
   } else if(combos[comboIndex].consecutiveLosses > 0) {
      streakBonus = -MathMin(combos[comboIndex].consecutiveLosses * 8.0, 40.0); // Max 40% penalty
   }

   // Bonus for sufficient trade volume (reliability)
   if(combos[comboIndex].trades >= 20) {
      tradeVolumeBonus = 10.0;
   } else if(combos[comboIndex].trades >= 10) {
      tradeVolumeBonus = 5.0;
   }

   // Calculate final confidence (0-100)
   combos[comboIndex].confidence = MathMax(0.0, MathMin(100.0,
      baseConfidence + streakBonus + tradeVolumeBonus));
}

//+------------------------------------------------------------------+
//| Get Best Performing Pattern with Confidence Filter              |
//+------------------------------------------------------------------+
string GetBestPattern()
{
   if(!learningInitialized) return "";

   double bestScore = -999999;
   string bestPattern = "";

   for(int i = 0; i < 4; i++) {
      if(combos[i].trades >= 5 &&
         combos[i].confidence >= adaptiveParams.currentMinConfidence &&
         combos[i].score > bestScore) {
         bestScore = combos[i].score;
         bestPattern = combos[i].pattern;
      }
   }

   return bestPattern;
}

//+------------------------------------------------------------------+
//| Adaptive Parameters Adjustment                                  |
//+------------------------------------------------------------------+
void AdaptParameters()
{
   // Only adjust every 10 trades to avoid over-optimization
   if(totalTrades % 10 != 0) return;

   // Don't adjust too frequently
   if(TimeCurrent() - adaptiveParams.lastAdjustment < 3600) return; // 1 hour minimum

   double currentWinRate = totalTrades > 0 ? (double)winTrades / totalTrades * 100.0 : 0;

   // Adjust TP multiplier based on win rate
   if(currentWinRate < 45.0) {
      // Win rate too low, reduce TP to increase hit rate
      adaptiveParams.currentTPMultiplier *= (1.0 - adaptiveParams.tpAdjustmentFactor);
      adaptiveParams.currentTPMultiplier = MathMax(0.8, adaptiveParams.currentTPMultiplier); // Min 0.8
      Print("📉 REDUCING TP: Win rate ", DoubleToString(currentWinRate, 1), "% too low. New TP multiplier: ",
            DoubleToString(adaptiveParams.currentTPMultiplier, 2));
   }
   else if(currentWinRate > 70.0) {
      // Win rate very high, can increase TP for better RR
      adaptiveParams.currentTPMultiplier *= (1.0 + adaptiveParams.tpAdjustmentFactor);
      adaptiveParams.currentTPMultiplier = MathMin(3.0, adaptiveParams.currentTPMultiplier); // Max 3.0
      Print("📈 INCREASING TP: Win rate ", DoubleToString(currentWinRate, 1), "% excellent. New TP multiplier: ",
            DoubleToString(adaptiveParams.currentTPMultiplier, 2));
   }

   // Adjust confidence requirement based on recent performance
   if(streakTracker.currentLossStreak >= 3) {
      // On loss streak, increase confidence requirement
      adaptiveParams.currentMinConfidence = MathMin(80.0, adaptiveParams.currentMinConfidence + 10.0);
      Print("⚠️ INCREASING confidence requirement to ", DoubleToString(adaptiveParams.currentMinConfidence, 1),
            "% due to loss streak");
   }
   else if(streakTracker.currentWinStreak >= 5) {
      // On win streak, can lower confidence requirement slightly
      adaptiveParams.currentMinConfidence = MathMax(30.0, adaptiveParams.currentMinConfidence - 5.0);
      Print("🔥 LOWERING confidence requirement to ", DoubleToString(adaptiveParams.currentMinConfidence, 1),
            "% due to win streak");
   }

   // Adjust cooldown based on performance
   if(currentWinRate > 60.0) {
      // Good performance, can trade more frequently
      adaptiveParams.currentCooldown = MathMax(5, (int)(CooldownSeconds * 0.8));
   }
   else if(currentWinRate < 40.0) {
      // Poor performance, trade less frequently
      adaptiveParams.currentCooldown = MathMin(60, (int)(CooldownSeconds * 1.5));
   }

   adaptiveParams.lastAdjustment = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Check if Current Hour is Profitable for Trading                 |
//+------------------------------------------------------------------+
bool IsCurrentHourProfitable()
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   int currentHour = dt.hour;

   // If not enough data, allow trading
   if(hourlyStats[currentHour].trades < 5) return true;

   return hourlyStats[currentHour].isActive;
}

//+------------------------------------------------------------------+
//| Enhanced Learning Statistics Display                            |
//+------------------------------------------------------------------+
void PrintLearningStats()
{
   if(!learningInitialized) return;

   Print("🧠 === ENHANCED LEARNING SYSTEM PERFORMANCE ===");

   // Pattern performance
   for(int i = 0; i < 4; i++) {
      if(combos[i].trades > 0) {
         Print("📈 ", combos[i].pattern, ": ", combos[i].trades, " trades | ",
               DoubleToString(combos[i].winRate, 1), "% win | $",
               DoubleToString(combos[i].avgProfit, 2), " avg | Confidence: ",
               DoubleToString(combos[i].confidence, 1), "% | Win Streak: ", combos[i].consecutiveWins);
      }
   }

   // Streak information
   Print("🔥 STREAK STATUS: ",
         streakTracker.isOnWinStreak ? "WIN STREAK " + IntegerToString(streakTracker.currentWinStreak) :
         streakTracker.isOnLossStreak ? "LOSS STREAK " + IntegerToString(streakTracker.currentLossStreak) : "NEUTRAL");

   // Current hour performance
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   int currentHour = dt.hour;
   if(hourlyStats[currentHour].trades > 0) {
      Print("⏰ HOUR ", currentHour, ": ", hourlyStats[currentHour].trades, " trades | ",
            DoubleToString(hourlyStats[currentHour].winRate, 1), "% win | ",
            hourlyStats[currentHour].isActive ? "ACTIVE" : "INACTIVE");
   }

   // Adaptive parameters
   Print("🎯 ADAPTIVE PARAMS: TP×", DoubleToString(adaptiveParams.currentTPMultiplier, 2),
         " | Min Confidence: ", DoubleToString(adaptiveParams.currentMinConfidence, 1), "%",
         " | Cooldown: ", adaptiveParams.currentCooldown, "s");

   string best = GetBestPattern();
   if(best != "") {
      Print("🏆 BEST PATTERN: ", best);
   }
}

//+------------------------------------------------------------------+
//| Check for Closed Trades and Update Learning                     |
//+------------------------------------------------------------------+
void CheckClosedTrades()
{
   static int lastDealsTotal = 0;
   int currentDealsTotal = HistoryDealsTotal();

   if(currentDealsTotal > lastDealsTotal) {
      // New deals found, check the latest ones
      for(int i = lastDealsTotal; i < currentDealsTotal; i++) {
         ulong ticket = HistoryDealGetTicket(i);
         if(ticket > 0) {
            string symbol = HistoryDealGetString(ticket, DEAL_SYMBOL);
            if(symbol == _Symbol) {
               long magic = HistoryDealGetInteger(ticket, DEAL_MAGIC);
               if(magic == 654321) { // Our EA's magic number
                  double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                  string comment = HistoryDealGetString(ticket, DEAL_COMMENT);

                  // Extract pattern from comment
                  string pattern = "";
                  if(StringFind(comment, "Pattern:") >= 0) {
                     pattern = StringSubstr(comment, 8); // Remove "Pattern:" prefix
                  }

                  if(pattern != "") {
                     UpdateLearning(pattern, profit);
                  }
               }
            }
         }
      }
   }

   lastDealsTotal = currentDealsTotal;
}

//+------------------------------------------------------------------+
int OnInit() {
   Print("🚀 === V75 1s ENHANCED Spike Hunter v6.0 - INTELLIGENT ADAPTIVE TRADING! ===");

   // Initialize reward-based learning system
   InitializeLearning();

   // Get symbol trading specifications
   minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   
   Print("Symbol specs - Min Lot: ", minLot, " | Max Lot: ", maxLot, " | Lot Step: ", lotStep);
   
   // Validate and adjust FixedLot
   adjustedFixedLot = FixedLot; // Use a modifiable copy
   if(adjustedFixedLot < minLot) {
      Print("WARNING: FixedLot (", FixedLot, ") below minimum (", minLot, "). Adjusting to minimum.");
      adjustedFixedLot = minLot;
   }
   if(adjustedFixedLot > maxLot) {
      Print("WARNING: FixedLot (", FixedLot, ") above maximum (", maxLot, "). Adjusting to maximum.");
      adjustedFixedLot = maxLot;
   }
   
   // Ensure FixedLot aligns with lot step
   adjustedFixedLot = NormalizeLot(adjustedFixedLot);
   Print("Adjusted FixedLot: ", adjustedFixedLot);
   
   // Create indicators for 1-minute timeframe
   atrHandle = iATR(_Symbol, PERIOD_M1, 14);
   emaFastHandle = iMA(_Symbol, PERIOD_M1, 5, 0, MODE_EMA, PRICE_CLOSE);
   emaSlowHandle = iMA(_Symbol, PERIOD_M1, 13, 0, MODE_EMA, PRICE_CLOSE);

   // Initialize additional indicators for better signal quality
   if(UseRSIFilter) {
      rsiHandle = iRSI(_Symbol, PERIOD_M1, 14, PRICE_CLOSE);
      if(rsiHandle == INVALID_HANDLE) {
         Print("ERROR: Failed to create RSI indicator");
         return(INIT_FAILED);
      }
   }

   if(UseMACDFilter) {
      macdHandle = iMACD(_Symbol, PERIOD_M1, 12, 26, 9, PRICE_CLOSE);
      if(macdHandle == INVALID_HANDLE) {
         Print("ERROR: Failed to create MACD indicator");
         return(INIT_FAILED);
      }
   }

   if(UseStochFilter) {
      stochHandle = iStochastic(_Symbol, PERIOD_M1, 5, 3, 3, MODE_SMA, STO_LOWHIGH);
      if(stochHandle == INVALID_HANDLE) {
         Print("ERROR: Failed to create Stochastic indicator");
         return(INIT_FAILED);
      }
   }

   if(atrHandle == INVALID_HANDLE || emaFastHandle == INVALID_HANDLE || emaSlowHandle == INVALID_HANDLE) {
      Print("ERROR: Failed to create core indicators");
      return(INIT_FAILED);
   }
   
   // Wait for indicators to load
   Sleep(2000);
   
   PrintBrokerInfo();
   PrintOptimizedSettings();
   Print("🎯 === EA READY - V75 1s ENHANCED Spike Hunter - INTELLIGENT ADAPTIVE TRADING! ===");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnTick() {
   static datetime lastBar = 0;
   datetime currentBar = iTime(_Symbol, PERIOD_M1, 0);
   if(lastBar == currentBar) return;
   lastBar = currentBar;

   // Check for closed trades and update learning system
   CheckClosedTrades();

   // Safety checks
   if(PositionsTotal() > 0) return;

   // Use adaptive cooldown instead of fixed cooldown
   if((TimeCurrent() - lastTradeTime) < adaptiveParams.currentCooldown) return;

   // Check if current hour is profitable for trading
   if(!IsCurrentHourProfitable()) {
      if(ShowDebug) Print("⏰ HOUR FILTER: Current hour not profitable, skipping trade");
      return;
   }

   // Get fresh indicator data
   if(!GetIndicatorData()) return;

   // Analyze previous completed candle
   AnalyzeAndTrade();

   // Manage existing positions with enhanced trailing stops
   ManageEnhancedTrailingStops();

   // Print learning stats every 100 ticks
   static int tickCount = 0;
   tickCount++;
   if(tickCount % 100 == 0) {
      PrintLearningStats();
   }
}

//+------------------------------------------------------------------+
bool GetIndicatorData() {
   double atr[], emaFast[], emaSlow[];
   ArraySetAsSeries(atr, true);
   ArraySetAsSeries(emaFast, true);
   ArraySetAsSeries(emaSlow, true);

   if(CopyBuffer(atrHandle, 0, 0, 3, atr) < 3 ||
      CopyBuffer(emaFastHandle, 0, 0, 3, emaFast) < 3 ||
      CopyBuffer(emaSlowHandle, 0, 0, 3, emaSlow) < 3) {
      if(ShowDebug) Print("Failed to get indicator data");
      return false;
   }

   lastATR = atr[1];
   return true;
}

//+------------------------------------------------------------------+
void AnalyzeAndTrade() {
   // Get previous completed candle data (index 1)
   double open = iOpen(_Symbol, PERIOD_M1, 1);
   double high = iHigh(_Symbol, PERIOD_M1, 1);
   double low = iLow(_Symbol, PERIOD_M1, 1);
   double close = iClose(_Symbol, PERIOD_M1, 1);
   
   if(open == 0 || high == 0 || low == 0 || close == 0) return;
   
   // Calculate spike metrics
   double candleRange = (high - low) / _Point;
   double bodySize = MathAbs(close - open) / _Point;
   double bodyRatio = candleRange > 0 ? bodySize / candleRange : 0;
   bool isBullish = close > open;
   
   // Additional spike quality metrics
   double upperWick = (isBullish ? (high - close) : (high - open)) / _Point;
   double lowerWick = (isBullish ? (open - low) : (close - low)) / _Point;
   double atrPoints = lastATR / _Point;
   double atrMultiplier = candleRange / atrPoints;
   
   if(ShowDebug) {
      Print("V75 1s Analysis: Range=", DoubleToString(candleRange, 1), 
            " | Body%=", DoubleToString(bodyRatio*100, 1), 
            " | ATR×=", DoubleToString(atrMultiplier, 2),
            " | Bull=", isBullish);
   }
   
   // Check if valid spike
   int sweetSpotUsed = 0;
   if(!IsValidSpike(candleRange, bodyRatio, atrMultiplier, sweetSpotUsed)) {
      if(ShowDebug) Print("❌ SPIKE REJECTED: Range=", DoubleToString(candleRange,1), " Body%=", DoubleToString(bodyRatio*100,1), " ATR×=", DoubleToString(atrMultiplier,2));
      return;
   }

   // Multi-indicator confirmation check with confidence scoring
   string confirmedPattern = "";
   double patternConfidence = 0.0;
   if(!GetMultiIndicatorConfirmation(isBullish, confirmedPattern, patternConfidence)) {
      if(ShowDebug) Print("❌ TRADE REJECTED: Multi-indicator confirmation failed");
      return;
   }

   // Check if pattern confidence meets adaptive requirement
   if(patternConfidence < adaptiveParams.currentMinConfidence) {
      if(ShowDebug) Print("❌ TRADE REJECTED: Pattern confidence ", DoubleToString(patternConfidence, 1),
                         "% below required ", DoubleToString(adaptiveParams.currentMinConfidence, 1), "%");
      return;
   }

   // Get trend direction and trade signal
   ENUM_ORDER_TYPE tradeType;
   if(!GetTradeSignal(isBullish, tradeType)) {
      if(ShowDebug) Print("❌ TRADE REJECTED: No valid trade signal");
      return;
   }

   // Execute trade with enhanced parameters
   ExecuteEnhancedTrade(tradeType, candleRange, bodyRatio, sweetSpotUsed, atrMultiplier, confirmedPattern, patternConfidence);
}

//+------------------------------------------------------------------+
bool IsValidSpike(double candleRange, double bodyRatio, double atrMultiplier, int &sweetSpotUsed) {
   sweetSpotUsed = 0;
   
   // ATR filter - spike must be significant relative to recent volatility
   if(atrMultiplier < MinATRMultiplier) {
      if(ShowDebug) Print("Spike rejected: ATR multiplier too low (", DoubleToString(atrMultiplier, 2), ")");
      return false;
   }
   
   // Test mode for debugging
   if(EnableTestMode) {
      if(candleRange >= 30.0 && bodyRatio >= 0.25) {
         sweetSpotUsed = 99; // Test mode indicator
         if(ShowDebug) Print("TEST MODE: Valid spike detected - Range=", candleRange, " Ratio=", bodyRatio);
         return true;
      }
      return false;
   }
   
   if(UseSweetSpots) {
      // Check Sweet Spot 1 (Small profitable spikes: 50-200 points)
      if(candleRange >= SweetSpot1_Min && candleRange <= SweetSpot1_Max && bodyRatio >= SweetSpot1_Ratio) {
         sweetSpotUsed = 1;
         if(ShowDebug) Print("Sweet Spot 1 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
      
      // Check Sweet Spot 2 (Medium spikes: 200-500 points)
      if(candleRange >= SweetSpot2_Min && candleRange <= SweetSpot2_Max && bodyRatio >= SweetSpot2_Ratio) {
         sweetSpotUsed = 2;
         if(ShowDebug) Print("Sweet Spot 2 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
      
      // Check Sweet Spot 3 (Large spikes: 500-1000 points)
      if(candleRange >= SweetSpot3_Min && candleRange <= SweetSpot3_Max && bodyRatio >= SweetSpot3_Ratio) {
         sweetSpotUsed = 3;
         if(ShowDebug) Print("Sweet Spot 3 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
      
      // Check Sweet Spot 4 (Extra large spikes: 1000-2000 points)
      if(candleRange >= SweetSpot4_Min && candleRange <= SweetSpot4_Max && bodyRatio >= SweetSpot4_Ratio) {
         sweetSpotUsed = 4;
         if(ShowDebug) Print("Sweet Spot 4 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
      
      // Handle extreme spikes beyond defined ranges
      if(candleRange > SweetSpot4_Max && bodyRatio >= 0.30) {
         sweetSpotUsed = 5; // Extreme spike zone
         if(ShowDebug) Print("Extreme Spike Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
   }
   else {
      // Base mode with updated parameters
      double bodySize = candleRange * bodyRatio;
      double atrPoints = lastATR / _Point;
      bool validSpike = candleRange >= BaseSpikeSize && 
                       bodyRatio >= BaseBodyRatio &&
                       bodySize >= (BaseSpikeFactor * atrPoints);
      
      if(validSpike && ShowDebug) {
         Print("Base Spike Hit: Range=", candleRange, " ATR=", DoubleToString(atrPoints,1), " Required=", BaseSpikeSize);
      }
      return validSpike;
   }
   
   return false;
}

//+------------------------------------------------------------------+
bool GetMultiIndicatorConfirmation(bool isBullish, string &confirmedPattern, double &confidence) {
   int confirmations = 0;
   int totalIndicators = 0;

   // Declare confirmation variables for pattern tracking
   bool rsiConfirmed = false, macdConfirmed = false, stochConfirmed = false;

   // RSI Confirmation
   if(UseRSIFilter) {
      totalIndicators++;
      double rsi[];
      ArraySetAsSeries(rsi, true);
      if(CopyBuffer(rsiHandle, 0, 0, 2, rsi) >= 2) {
         string rsiSignal = "";

         if(isBullish && rsi[1] < RSI_Oversold) {
            confirmations++;
            rsiConfirmed = true;
            rsiSignal = "BUY_CONFIRMED";
         } else if(!isBullish && rsi[1] > RSI_Overbought) {
            confirmations++;
            rsiConfirmed = true;
            rsiSignal = "SELL_CONFIRMED";
         } else if(isBullish && rsi[1] > RSI_Overbought) {
            rsiSignal = "SELL_CONFLICT";
         } else if(!isBullish && rsi[1] < RSI_Oversold) {
            rsiSignal = "BUY_CONFLICT";
         } else {
            rsiSignal = "NEUTRAL";
         }

         if(ShowDebug) Print("RSI: ", DoubleToString(rsi[1], 1),
                           " | Zone: ", (rsi[1] < RSI_Oversold ? "OVERSOLD(<40)" : rsi[1] > RSI_Overbought ? "OVERBOUGHT(>60)" : "NEUTRAL"),
                           " | Signal: ", rsiSignal,
                           " | Confirmed: ", rsiConfirmed);
      }
   }

   // MACD Confirmation
   if(UseMACDFilter) {
      totalIndicators++;
      double macd[], signal[];
      ArraySetAsSeries(macd, true);
      ArraySetAsSeries(signal, true);
      if(CopyBuffer(macdHandle, 0, 0, 2, macd) >= 2 && CopyBuffer(macdHandle, 1, 0, 2, signal) >= 2) {
         bool macdBullish = macd[1] > signal[1] && macd[0] > macd[1]; // MACD above signal and rising
         bool macdBearish = macd[1] < signal[1] && macd[0] < macd[1]; // MACD below signal and falling
         string macdSignal = "";

         if(isBullish && macdBullish) {
            confirmations++;
            macdConfirmed = true;
            macdSignal = "BUY_CONFIRMED";
         } else if(!isBullish && macdBearish) {
            confirmations++;
            macdConfirmed = true;
            macdSignal = "SELL_CONFIRMED";
         } else if(isBullish && macdBearish) {
            macdSignal = "SELL_CONFLICT";
         } else if(!isBullish && macdBullish) {
            macdSignal = "BUY_CONFLICT";
         } else {
            macdSignal = "NEUTRAL";
         }

         if(ShowDebug) Print("MACD: ", DoubleToString(macd[1], 5),
                           " | Signal: ", DoubleToString(signal[1], 5),
                           " | Trend: ", (macdBullish ? "BULL" : macdBearish ? "BEAR" : "NEUTRAL"),
                           " | Result: ", macdSignal,
                           " | Confirmed: ", macdConfirmed);
      }
   }

   // Stochastic Confirmation
   if(UseStochFilter) {
      totalIndicators++;
      double stochMain[], stochSignal[];
      ArraySetAsSeries(stochMain, true);
      ArraySetAsSeries(stochSignal, true);
      if(CopyBuffer(stochHandle, 0, 0, 2, stochMain) >= 2 && CopyBuffer(stochHandle, 1, 0, 2, stochSignal) >= 2) {
         bool stochOversold = stochMain[1] < 30 && stochSignal[1] < 30;  // Relaxed for V75 1s
         bool stochOverbought = stochMain[1] > 70 && stochSignal[1] > 70; // Relaxed for V75 1s
         string stochResult = "";

         if(isBullish && stochOversold) {
            confirmations++;
            stochConfirmed = true;
            stochResult = "BUY_CONFIRMED";
         } else if(!isBullish && stochOverbought) {
            confirmations++;
            stochConfirmed = true;
            stochResult = "SELL_CONFIRMED";
         } else if(isBullish && stochOverbought) {
            stochResult = "SELL_CONFLICT";
         } else if(!isBullish && stochOversold) {
            stochResult = "BUY_CONFLICT";
         } else {
            stochResult = "NEUTRAL";
         }

         if(ShowDebug) Print("Stoch: ", DoubleToString(stochMain[1], 1),
                           " | Signal: ", DoubleToString(stochSignal[1], 1),
                           " | Zone: ", (stochOversold ? "OVERSOLD(<30)" : stochOverbought ? "OVERBOUGHT(>70)" : "NEUTRAL"),
                           " | Result: ", stochResult,
                           " | Confirmed: ", stochConfirmed);
      }
   }

   if(totalIndicators == 0) return true; // No additional filters enabled

   // Require minimum confirmations based on learning system
   bool confirmed = RequireMultiConfirm ? (confirmations >= MinConfirmations) : (confirmations > 0);

   // Track which indicators confirmed for learning
   confirmedPattern = "";
   if(UseRSIFilter && rsiConfirmed) confirmedPattern += "RSI+";
   if(UseMACDFilter && macdConfirmed) confirmedPattern += "MACD+";
   if(UseStochFilter && stochConfirmed) confirmedPattern += "STOCH+";
   if(StringLen(confirmedPattern) > 0) confirmedPattern = StringSubstr(confirmedPattern, 0, StringLen(confirmedPattern)-1);

   // Calculate confidence based on confirmations and historical performance
   confidence = 0.0;
   if(confirmed && confirmedPattern != "") {
      // Base confidence from confirmation ratio
      double baseConfidence = (double)confirmations / totalIndicators * 100.0;

      // Find pattern in combos and get its historical confidence
      double historicalConfidence = 50.0; // Default
      for(int i = 0; i < 4; i++) {
         if(combos[i].pattern == confirmedPattern ||
            (confirmedPattern == "RSI+MACD+STOCH" && combos[i].pattern == "ALL_THREE")) {
            historicalConfidence = combos[i].confidence;
            break;
         }
      }

      // Combine base and historical confidence
      confidence = (baseConfidence + historicalConfidence) / 2.0;

      // Bonus for multiple confirmations
      if(confirmations >= 2) confidence += 10.0;
      if(confirmations >= 3) confidence += 15.0;

      // Streak bonus/penalty
      if(streakTracker.isOnWinStreak) {
         confidence += MathMin(streakTracker.currentWinStreak * 3.0, 15.0);
      } else if(streakTracker.isOnLossStreak) {
         confidence -= MathMin(streakTracker.currentLossStreak * 5.0, 25.0);
      }

      confidence = MathMax(0.0, MathMin(100.0, confidence));
   }

   // Store pattern for trade tracking
   if(confirmed) lastTradePattern = confirmedPattern;

   if(ShowDebug) {
      Print("Multi-Indicator Analysis: ", confirmations, "/", totalIndicators,
            " | Pattern: ", confirmedPattern,
            " | Confidence: ", DoubleToString(confidence, 1), "%",
            " | Required: ", (RequireMultiConfirm ? IntegerToString(MinConfirmations) + "+" : "Any"),
            " | Result: ", (confirmed ? "PASS ✅" : "FAIL ❌"));
   }

   return confirmed;
}

//+------------------------------------------------------------------+
bool GetTradeSignal(bool isBullish, ENUM_ORDER_TYPE &tradeType) {
   // If EMA filter is disabled, trade based on spike direction only
   if(!UseEMAFilter) {
      if(UseContrarian) {
         // Trade against the spike (contrarian approach)
         tradeType = isBullish ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
         if(ShowDebug) Print("✅ CONTRARIAN Signal: ", EnumToString(tradeType), " against ", (isBullish ? "bullish" : "bearish"), " spike");
      } else {
         // Trade with the spike (momentum approach)
         tradeType = isBullish ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
         if(ShowDebug) Print("✅ MOMENTUM Signal: ", EnumToString(tradeType), " with ", (isBullish ? "bullish" : "bearish"), " spike");
      }
      return true;
   }
   
   // EMA trend filtering enabled
   double emaFast[], emaSlow[];
   ArraySetAsSeries(emaFast, true);
   ArraySetAsSeries(emaSlow, true);
   
   if(CopyBuffer(emaFastHandle, 0, 0, 2, emaFast) < 2 ||
      CopyBuffer(emaSlowHandle, 0, 0, 2, emaSlow) < 2) {
      return false;
   }
   
   bool upTrend = emaFast[1] > emaSlow[1];
   bool downTrend = emaFast[1] < emaSlow[1];
   
   if(UseContrarian) {
      // Contrarian approach with trend filter
      if(isBullish && downTrend) {
         tradeType = ORDER_TYPE_SELL;
         if(ShowDebug) Print("SELL Signal: Bullish spike reversal in downtrend");
         return true;
      }
      
      if(!isBullish && upTrend) {
         tradeType = ORDER_TYPE_BUY;
         if(ShowDebug) Print("BUY Signal: Bearish spike reversal in uptrend");
         return true;
      }
   } else {
      // Momentum approach with trend filter
      if(isBullish && upTrend) {
         tradeType = ORDER_TYPE_BUY;
         if(ShowDebug) Print("BUY Signal: Bullish spike in uptrend");
         return true;
      }
      
      if(!isBullish && downTrend) {
         tradeType = ORDER_TYPE_SELL;
         if(ShowDebug) Print("SELL Signal: Bearish spike in downtrend");
         return true;
      }
   }
   
   if(ShowDebug) Print("No valid signal: Spike/trend mismatch");
   return false;
}

//+------------------------------------------------------------------+
//| Enhanced Trade Execution with Adaptive Parameters               |
//+------------------------------------------------------------------+
void ExecuteEnhancedTrade(ENUM_ORDER_TYPE type, double spike, double bodyRatio, int sweetSpot,
                         double atrMult, string pattern, double confidence) {
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double price = (type == ORDER_TYPE_BUY) ? ask : bid;

   // Calculate dynamic SL using adaptive multiplier
   double slDistance = MathMax(lastATR * adaptiveParams.currentSLMultiplier, spike * _Point * SpikeMultiplier);
   double sl = (type == ORDER_TYPE_BUY) ? price - slDistance : price + slDistance;

   // Calculate dynamic TP using adaptive multiplier - KEY IMPROVEMENT FOR WIN RATE
   double tpDistance = slDistance * adaptiveParams.currentTPMultiplier;

   // Further reduce TP based on confidence and recent performance
   if(confidence < 60.0) {
      tpDistance *= 0.8; // Reduce TP by 20% for lower confidence trades
   }
   if(streakTracker.currentLossStreak >= 2) {
      tpDistance *= 0.7; // Reduce TP by 30% during loss streaks
   }
   if(totalTrades > 20) {
      double currentWinRate = (double)winTrades / totalTrades * 100.0;
      if(currentWinRate < 50.0) {
         tpDistance *= 0.6; // Aggressive TP reduction if win rate is low
      }
   }

   double tp = (type == ORDER_TYPE_BUY) ? price + tpDistance : price - tpDistance;

   double lot = CalculateLotSize(slDistance / _Point);

   ulong ticket = SendEnhancedOrder(type, price, sl, tp, lot, pattern);
   if(ticket > 0) {
      lastTradeTime = TimeCurrent();
      totalTrades++;

      string spotName;
      switch(sweetSpot) {
         case 99: spotName = "TEST"; break;
         case 0:  spotName = "BASE"; break;
         case 5:  spotName = "EXTREME"; break;
         default: spotName = "SPOT" + (string)sweetSpot; break;
      }

      Print("🚀 ENHANCED TRADE EXECUTED: ", EnumToString(type), " | Zone: ", spotName,
            " | Spike: ", DoubleToString(spike, 1), " (", DoubleToString(bodyRatio*100, 1), "%)",
            " | Pattern: ", pattern, " | Confidence: ", DoubleToString(confidence, 1), "%",
            " | TP Multiplier: ", DoubleToString(adaptiveParams.currentTPMultiplier, 2),
            " | Lot: ", lot, " | Ticket: ", ticket);
   }
   else {
      Print("❌ ENHANCED TRADE FAILED: Could not execute ", EnumToString(type), " order");
   }
}

//+------------------------------------------------------------------+
//| Legacy Trade Execution (kept for compatibility)                 |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE type, double spike, double bodyRatio, int sweetSpot, double atrMult) {
   // Call enhanced version with default values
   ExecuteEnhancedTrade(type, spike, bodyRatio, sweetSpot, atrMult, lastTradePattern, 50.0);
}

//+------------------------------------------------------------------+
double NormalizeLot(double lot) {
   if(lotStep == 0) return lot;
   
   // Round to nearest lot step
   double normalized = MathRound(lot / lotStep) * lotStep;
   
   // Ensure within min/max bounds
   normalized = MathMax(minLot, MathMin(maxLot, normalized));
   
   return normalized;
}

//+------------------------------------------------------------------+
double CalculateLotSize(double riskPips) {
   if(UseFixedLot) {
      Print("Using fixed lot size: ", adjustedFixedLot);
      return adjustedFixedLot;
   }
   
   if(riskPips <= 0) {
      Print("Invalid risk pips, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }
   
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   if(balance <= 0) {
      Print("Invalid balance, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }
   
   double riskAmount = balance * RiskPercent / 100.0;
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(tickSize <= 0 || tickValue <= 0) {
      Print("Invalid tick data, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }
   
   double pointValue = tickValue * (_Point / tickSize);
   double calculatedLot = riskAmount / (riskPips * pointValue);
   
   // Apply maximum lot limit
   calculatedLot = MathMin(calculatedLot, MaxLot);
   
   // Normalize the lot size
   double finalLot = NormalizeLot(calculatedLot);
   
   if(ShowDebug) {
      Print("Lot calculation: Risk$=", DoubleToString(riskAmount, 2), 
            " | RiskPips=", DoubleToString(riskPips, 1),
            " | Calculated=", DoubleToString(calculatedLot, 3),
            " | Final=", DoubleToString(finalLot, 3));
   }
   
   return finalLot;
}

//+------------------------------------------------------------------+
ENUM_ORDER_TYPE_FILLING GetFillingMode() {
   int filling = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);
   
   if((filling & SYMBOL_FILLING_FOK) != 0) return ORDER_FILLING_FOK;
   if((filling & SYMBOL_FILLING_IOC) != 0) return ORDER_FILLING_IOC;
   return ORDER_FILLING_FOK;
}

//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Enhanced Order Sending with Pattern Tracking                   |
//+------------------------------------------------------------------+
ulong SendEnhancedOrder(ENUM_ORDER_TYPE type, double price, double sl, double tp, double lot, string pattern) {
   // Final validation of lot size before sending order
   if(lot < minLot || lot > maxLot) {
      Print("ERROR: Lot size ", lot, " outside valid range [", minLot, " - ", maxLot, "]. Using adjusted fixed lot.");
      lot = adjustedFixedLot;
   }

   // Ensure lot size is properly normalized
   lot = NormalizeLot(lot);

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lot;
   request.type = type;
   request.price = NormalizeDouble(price, _Digits);
   request.sl = NormalizeDouble(sl, _Digits);
   request.tp = NormalizeDouble(tp, _Digits);
   request.deviation = MaxSlippage;
   request.magic = 654321;
   request.comment = "Enhanced_V75_Pattern:" + pattern;
   request.type_filling = GetFillingMode();

   Print("🚀 Sending enhanced order: ", EnumToString(type), " | Lot: ", lot, " | Price: ", price, " | Pattern: ", pattern);

   bool success = OrderSend(request, result);

   if(!success) {
      Print("Order failed: ", result.comment, " | Retcode: ", result.retcode);

      // Try alternative filling mode
      if(result.retcode == TRADE_RETCODE_INVALID_FILL) {
         Print("Trying alternative filling mode...");
         request.type_filling = (request.type_filling == ORDER_FILLING_FOK) ? ORDER_FILLING_IOC : ORDER_FILLING_FOK;
         success = OrderSend(request, result);

         if(!success) {
            Print("Both filling modes failed. Trying market order without SL/TP...");
            request.sl = 0;
            request.tp = 0;
            success = OrderSend(request, result);

            if(success && (sl > 0 || tp > 0)) {
               Sleep(100);
               ModifyPosition(result.order, sl, tp);
            }
         }
      }
   }

   if(success) {
      Print("✅ Enhanced order successful: Ticket ", result.order, " | Pattern: ", pattern);
   } else {
      Print("❌ Final enhanced order failure: ", result.comment, " | Retcode: ", result.retcode);
   }

   return success ? result.order : 0;
}

//+------------------------------------------------------------------+
//| Legacy Order Sending (kept for compatibility)                   |
//+------------------------------------------------------------------+
ulong SendOrder(ENUM_ORDER_TYPE type, double price, double sl, double tp, double lot) {
   return SendEnhancedOrder(type, price, sl, tp, lot, lastTradePattern);
}

//+------------------------------------------------------------------+
//| Enhanced Trailing Stops with Breakeven and Adaptive Logic      |
//+------------------------------------------------------------------+
void ManageEnhancedTrailingStops() {
   for(int i = PositionsTotal()-1; i >= 0; i--) {
      ulong ticket = PositionGetTicket(i);
      if(!PositionSelectByTicket(ticket) || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

      double currentPrice = SymbolInfoDouble(_Symbol, (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? SYMBOL_BID : SYMBOL_ASK);
      double currentSL = PositionGetDouble(POSITION_SL);
      double currentTP = PositionGetDouble(POSITION_TP);
      double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
      double profit = PositionGetDouble(POSITION_PROFIT);

      ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

      // Calculate dynamic trailing distance based on spike characteristics and ATR
      double baseTrailDist = TrailPips * _Point;
      double atrTrailDist = lastATR * 0.3; // 30% of ATR for tighter trailing
      double trailDist = MathMin(baseTrailDist, atrTrailDist);

      // Breakeven mechanism - move SL to breakeven when 50% of TP distance is reached
      double tpDistance = MathAbs(currentTP - openPrice);
      double breakevenThreshold = tpDistance * 0.5; // 50% of TP distance

      if(posType == POSITION_TYPE_BUY) {
         double unrealizedProfit = currentPrice - openPrice;

         // Breakeven logic
         if(unrealizedProfit >= breakevenThreshold && currentSL < openPrice) {
            double breakevenSL = openPrice + (5 * _Point); // Small buffer above breakeven
            ModifyPosition(ticket, breakevenSL, currentTP);
            Print("🛡️ BREAKEVEN activated for BUY position ", ticket, " | SL moved to: ", breakevenSL);
            continue; // Skip trailing logic this time
         }

         // Enhanced trailing logic
         double newSL = currentPrice - trailDist;

         // Only trail if we're in profit and new SL is better
         if(unrealizedProfit > 0 && newSL > currentSL + _Point && newSL > openPrice) {
            // Additional safety: don't trail too aggressively during loss streaks
            if(streakTracker.currentLossStreak >= 3) {
               trailDist *= 1.5; // Wider trailing during loss streaks
               newSL = currentPrice - trailDist;
            }

            ModifyPosition(ticket, newSL, currentTP);
            if(ShowDebug) Print("📈 Enhanced trailing BUY SL updated to: ", newSL, " | Trail dist: ", DoubleToString(trailDist/_Point, 1), " pips");
         }
      }
      else { // SELL position
         double unrealizedProfit = openPrice - currentPrice;

         // Breakeven logic
         if(unrealizedProfit >= breakevenThreshold && (currentSL == 0 || currentSL > openPrice)) {
            double breakevenSL = openPrice - (5 * _Point); // Small buffer below breakeven
            ModifyPosition(ticket, breakevenSL, currentTP);
            Print("🛡️ BREAKEVEN activated for SELL position ", ticket, " | SL moved to: ", breakevenSL);
            continue; // Skip trailing logic this time
         }

         // Enhanced trailing logic
         double newSL = currentPrice + trailDist;

         // Only trail if we're in profit and new SL is better
         if(unrealizedProfit > 0 && (currentSL == 0 || newSL < currentSL - _Point) && newSL < openPrice) {
            // Additional safety: don't trail too aggressively during loss streaks
            if(streakTracker.currentLossStreak >= 3) {
               trailDist *= 1.5; // Wider trailing during loss streaks
               newSL = currentPrice + trailDist;
            }

            ModifyPosition(ticket, newSL, currentTP);
            if(ShowDebug) Print("📉 Enhanced trailing SELL SL updated to: ", newSL, " | Trail dist: ", DoubleToString(trailDist/_Point, 1), " pips");
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Legacy Trailing Stops (kept for compatibility)                 |
//+------------------------------------------------------------------+
void ManageTrailingStops() {
   ManageEnhancedTrailingStops();
}

//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double sl, double tp) {
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_SLTP;
   request.position = ticket;
   request.sl = NormalizeDouble(sl, _Digits);
   request.tp = NormalizeDouble(tp, _Digits);
   
   return OrderSend(request, result);
}

//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction &trans, const MqlTradeRequest &request, const MqlTradeResult &result) {
   // Only process position closing transactions
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {

      if(HistoryDealSelect(trans.deal)) {
         // Check if this is a closing deal (exit from position)
         ENUM_DEAL_ENTRY dealEntry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(trans.deal, DEAL_ENTRY);

         if(dealEntry == DEAL_ENTRY_OUT) {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            double swap = HistoryDealGetDouble(trans.deal, DEAL_SWAP);
            double commission = HistoryDealGetDouble(trans.deal, DEAL_COMMISSION);
            double totalProfit = profit + swap + commission;
            string comment = HistoryDealGetString(trans.deal, DEAL_COMMENT);

            bool isWin = totalProfit > 0.01;
            if(isWin) winTrades++; // Consider small positive as win

            // Extract pattern from comment for enhanced learning
            string pattern = "";
            if(StringFind(comment, "Pattern:") >= 0) {
               int patternStart = StringFind(comment, "Pattern:") + 8;
               pattern = StringSubstr(comment, patternStart);
            }

            // Update enhanced learning system
            if(pattern != "") {
               UpdateLearning(pattern, totalProfit);
            }

            // Enhanced trade result display
            string resultIcon = isWin ? "🎉 WIN" : "💔 LOSS";
            string streakInfo = "";
            if(streakTracker.isOnWinStreak) {
               streakInfo = " | 🔥 Win Streak: " + IntegerToString(streakTracker.currentWinStreak);
            } else if(streakTracker.isOnLossStreak) {
               streakInfo = " | ⚠️ Loss Streak: " + IntegerToString(streakTracker.currentLossStreak);
            }

            Print("🏁 ENHANCED TRADE CLOSED: ", resultIcon,
                  " | Profit: $", DoubleToString(totalProfit, 2),
                  " | Pattern: ", (pattern != "" ? pattern : "Unknown"),
                  streakInfo,
                  " | P&L: $", DoubleToString(profit, 2),
                  " | Swap: $", DoubleToString(swap, 2),
                  " | Comm: $", DoubleToString(commission, 2));

            PrintEnhancedStats();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Enhanced Statistics Display                                     |
//+------------------------------------------------------------------+
void PrintEnhancedStats() {
   double winRate = totalTrades > 0 ? (double)winTrades / totalTrades * 100.0 : 0;

   Print("🚀 === ENHANCED V75 1s TRADING STATS ===");
   Print("📊 Total Trades: ", totalTrades);
   Print("🎯 Wins: ", winTrades, " (", DoubleToString(winRate, 1), "%)");
   Print("📈 Current ATR: ", DoubleToString(lastATR/_Point, 1), " points");

   // Streak information
   if(streakTracker.isOnWinStreak) {
      Print("🔥 Current Win Streak: ", streakTracker.currentWinStreak, " | Best: ", streakTracker.maxWinStreak);
   } else if(streakTracker.isOnLossStreak) {
      Print("⚠️ Current Loss Streak: ", streakTracker.currentLossStreak, " | Worst: ", streakTracker.maxLossStreak);
   }

   // Adaptive parameters status
   Print("🎯 Adaptive TP Multiplier: ", DoubleToString(adaptiveParams.currentTPMultiplier, 2));
   Print("🛡️ Min Confidence Required: ", DoubleToString(adaptiveParams.currentMinConfidence, 1), "%");
   Print("⏱️ Current Cooldown: ", adaptiveParams.currentCooldown, "s");

   // Current hour performance
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   int currentHour = dt.hour;
   if(hourlyStats[currentHour].trades > 0) {
      Print("⏰ Hour ", currentHour, " Performance: ", hourlyStats[currentHour].trades, " trades | ",
            DoubleToString(hourlyStats[currentHour].winRate, 1), "% win | ",
            hourlyStats[currentHour].isActive ? "ACTIVE ✅" : "INACTIVE ❌");
   }

   Print("==========================================");
}

//+------------------------------------------------------------------+
//| Legacy Stats Function (kept for compatibility)                  |
//+------------------------------------------------------------------+
void PrintStats() {
   PrintEnhancedStats();
}

//+------------------------------------------------------------------+
void PrintBrokerInfo() {
   Print("=== BROKER INFO ===");
   Print("Symbol: ", _Symbol);
   Print("Point: ", _Point, " | Digits: ", _Digits);
   Print("Min Lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN));
   Print("Max Lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX));
   Print("Lot Step: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP));
   Print("===================");
}

//+------------------------------------------------------------------+
void PrintOptimizedSettings() {
   Print("=== V75 1s EA OPTIMIZED SETTINGS ===");
   Print("Sweet Spots: ", UseSweetSpots ? "ENABLED" : "DISABLED");
   Print("EMA Filter: ", UseEMAFilter ? "ENABLED" : "DISABLED");
   Print("Strategy: ", UseContrarian ? "CONTRARIAN" : "MOMENTUM");
   Print("Test Mode: ", EnableTestMode ? "ENABLED" : "DISABLED");
   Print("Use Fixed Lot: ", UseFixedLot ? "YES" : "NO");
   Print("Fixed Lot Size: ", adjustedFixedLot);
   Print("Max Lot Limit: ", MaxLot);
   
   if(UseSweetSpots && !EnableTestMode) {
      Print("Spot1: ", SweetSpot1_Min, "-", SweetSpot1_Max, " pts (", DoubleToString(SweetSpot1_Ratio*100,1), "% body)");
      Print("Spot2: ", SweetSpot2_Min, "-", SweetSpot2_Max, " pts (", DoubleToString(SweetSpot2_Ratio*100,1), "% body)");
      Print("Spot3: ", SweetSpot3_Min, "-", SweetSpot3_Max, " pts (", DoubleToString(SweetSpot3_Ratio*100,1), "% body)");
      Print("Spot4: ", SweetSpot4_Min, "-", SweetSpot4_Max, " pts (", DoubleToString(SweetSpot4_Ratio*100,1), "% body)");
   }
   
   Print("Risk: ", RiskPercent, "% | RR: ", RRRatio, " | Trail: ", TrailPips, " pips");
   Print("ATR×: ", ATRMultiplier, " | Spike×: ", SpikeMultiplier);
   Print("Cooldown: ", CooldownSeconds, "s | Min ATR×: ", MinATRMultiplier);
   Print("FILTERS: RSI=", UseRSIFilter, " MACD=", UseMACDFilter, " Stoch=", UseStochFilter);
   Print("Multi-Confirm Required: ", RequireMultiConfirm, " (60% threshold)");
   Print("====================================");
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
   if(emaFastHandle != INVALID_HANDLE) IndicatorRelease(emaFastHandle);
   if(emaSlowHandle != INVALID_HANDLE) IndicatorRelease(emaSlowHandle);
   if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
   if(macdHandle != INVALID_HANDLE) IndicatorRelease(macdHandle);
   if(stochHandle != INVALID_HANDLE) IndicatorRelease(stochHandle);

   // Print final enhanced statistics
   PrintEnhancedStats();
   PrintLearningStats();

   // Print hourly performance summary
   Print("📊 === HOURLY PERFORMANCE SUMMARY ===");
   for(int h = 0; h < 24; h++) {
      if(hourlyStats[h].trades >= 3) {
         Print("Hour ", h, ": ", hourlyStats[h].trades, " trades | ",
               DoubleToString(hourlyStats[h].winRate, 1), "% win | ",
               DoubleToString(hourlyStats[h].avgProfit, 2), " avg | ",
               hourlyStats[h].isActive ? "ACTIVE" : "INACTIVE");
      }
   }

   Print("🚀 === V75 1s ENHANCED Spike Hunter v6.0 - INTELLIGENT ADAPTIVE TRADING STOPPED ===");
   Print("🧠 Enhanced learning system has collected valuable performance data for future optimization!");
}